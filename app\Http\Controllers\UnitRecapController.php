<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DailyReport;
use App\Models\Unit;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UnitRecapController extends Controller
{
    /**
     * Display the unit recap dashboard
     */
    public function index()
    {
        return view('unit-recap.index');
    }

    /**
     * Get problem component chart data
     */
    public function getProblemComponentData(Request $request)
    {
        $query = DailyReport::select('problem_component', DB::raw('count(*) as count'))
            ->whereNotNull('problem_component')
            ->where('problem_component', '!=', '');

        // Apply date filters if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('date_in', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('date_in', '<=', $request->end_date);
        }

        $data = $query->groupBy('problem_component')
            ->orderBy('count', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get detailed reports for a specific problem component
     */
    public function getProblemComponentDetails(Request $request, $problemComponent)
    {
        $query = DailyReport::with(['unit', 'jobs', 'technicians'])
            ->where('problem_component', $problemComponent);

        // Apply date filters if provided
        if ($request->has('start_date') && $request->start_date) {
            $query->where('date_in', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->where('date_in', '<=', $request->end_date);
        }

        $reports = $query->orderBy('date_in', 'desc')->get();

        // Format the data for display
        $formattedReports = $reports->map(function ($report) {
            // Format date in Indonesian style
            $date = Carbon::parse($report->date_in);
            $months = [
                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
            ];
            $dateIn = $date->day . ' ' . $months[$date->month - 1] . ' ' . $date->year;

            // Calculate DT Hours
            $startTime = Carbon::createFromFormat('H:i:s', $report->hour_in);
            $endTime = Carbon::createFromFormat('H:i:s', $report->hour_out);
            $dtMinutes = $endTime->diffInMinutes($startTime);

            // Handle overnight shifts
            if ($dtMinutes < 0) {
                $dtMinutes += 24 * 60;
            }

            // Format DT display
            $dtDisplay = $dtMinutes > 59 
                ? floor($dtMinutes / 60) . ' jam ' . ($dtMinutes % 60) . ' menit'
                : $dtMinutes . ' menit';

            return [
                'daily_report_id' => $report->daily_report_id,
                'date_in' => $dateIn,
                'unit_code' => $report->unit ? $report->unit->unit_code : '-',
                'unit_type' => $report->unit ? $report->unit->unit_type : '-',
                'hm' => $report->hm,
                'problem' => $report->problem,
                'problem_component' => $report->problem_component,
                'problem_description' => $report->problem_description,
                'hour_in' => $report->hour_in,
                'hour_out' => $report->hour_out,
                'shift' => $report->shift,
                'dt_display' => $dtDisplay,
                'jobs' => $report->jobs->map(function ($job) {
                    return [
                        'job_description' => $job->job_description,
                        'highlight' => $job->highlight
                    ];
                }),
                'technicians' => $report->technicians->map(function ($technician) {
                    return $technician->name;
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedReports,
            'problem_component' => $problemComponent
        ]);
    }

    /**
     * Search units that have daily reports
     */
    public function searchUnits(Request $request)
    {
        $search = $request->get('search', '');
        
        $units = Unit::select('units.id', 'units.unit_code', 'units.unit_type')
            ->join('daily_reports', 'units.id', '=', 'daily_reports.unit_id')
            ->where(function ($query) use ($search) {
                $query->where('units.unit_code', 'LIKE', "%{$search}%")
                      ->orWhere('units.unit_type', 'LIKE', "%{$search}%");
            })
            ->distinct()
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $units
        ]);
    }

    /**
     * Get report dates for a specific unit
     */
    public function getUnitReportDates($unitId)
    {
        $dates = DailyReport::where('unit_id', $unitId)
            ->select('date_in')
            ->distinct()
            ->orderBy('date_in', 'desc')
            ->get()
            ->map(function ($item) {
                $date = Carbon::parse($item->date_in);
                $months = [
                    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                ];
                return [
                    'date_raw' => $item->date_in,
                    'date_formatted' => $date->day . ' ' . $months[$date->month - 1] . ' ' . $date->year
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $dates
        ]);
    }

    /**
     * Get daily reports for a specific unit and date
     */
    public function getUnitDateReports($unitId, $date)
    {
        $reports = DailyReport::with(['unit', 'jobs', 'technicians'])
            ->where('unit_id', $unitId)
            ->where('date_in', $date)
            ->get();

        // Format the data similar to getProblemComponentDetails
        $formattedReports = $reports->map(function ($report) {
            // Format date in Indonesian style
            $date = Carbon::parse($report->date_in);
            $months = [
                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
            ];
            $dateIn = $date->day . ' ' . $months[$date->month - 1] . ' ' . $date->year;

            // Calculate DT Hours
            $startTime = Carbon::createFromFormat('H:i:s', $report->hour_in);
            $endTime = Carbon::createFromFormat('H:i:s', $report->hour_out);
            $dtMinutes = $endTime->diffInMinutes($startTime);

            // Handle overnight shifts
            if ($dtMinutes < 0) {
                $dtMinutes += 24 * 60;
            }

            // Format DT display
            $dtDisplay = $dtMinutes > 59 
                ? floor($dtMinutes / 60) . ' jam ' . ($dtMinutes % 60) . ' menit'
                : $dtMinutes . ' menit';

            return [
                'daily_report_id' => $report->daily_report_id,
                'date_in' => $dateIn,
                'unit_code' => $report->unit ? $report->unit->unit_code : '-',
                'unit_type' => $report->unit ? $report->unit->unit_type : '-',
                'hm' => $report->hm,
                'problem' => $report->problem,
                'problem_component' => $report->problem_component,
                'problem_description' => $report->problem_description,
                'hour_in' => $report->hour_in,
                'hour_out' => $report->hour_out,
                'shift' => $report->shift,
                'dt_display' => $dtDisplay,
                'jobs' => $report->jobs->map(function ($job) {
                    return [
                        'job_description' => $job->job_description,
                        'highlight' => $job->highlight
                    ];
                }),
                'technicians' => $report->technicians->map(function ($technician) {
                    return $technician->name;
                })
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $formattedReports
        ]);
    }
}
