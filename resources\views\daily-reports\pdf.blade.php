<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>TAR - Daily Reports</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        .header h2 {
            margin: 5px 0;
            font-size: 14px;
            font-weight: normal;
        }
        .period {
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .report-item {
            margin-bottom: 25px;
            border: 1px solid #ccc;
            padding: 15px;
            page-break-inside: avoid;
        }
        .report-header {
            background-color: #f5f5f5;
            padding: 10px;
            margin: -15px -15px 15px -15px;
            border-bottom: 1px solid #ccc;
        }
        .report-header h3 {
            margin: 0;
            font-size: 14px;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .info-row {
            display: table-row;
        }
        .info-label {
            display: table-cell;
            width: 150px;
            font-weight: bold;
            padding: 3px 0;
            vertical-align: top;
        }
        .info-value {
            display: table-cell;
            padding: 3px 0;
            vertical-align: top;
        }
        .jobs-section, .technicians-section {
            margin-top: 15px;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .jobs-list, .technicians-list {
            margin-left: 20px;
        }
        .job-item {
            margin-bottom: 3px;
        }
        .highlighted-job {
            background-color: #225297;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
        }
        .images-section {
            margin-top: 15px;
        }
        .images-grid {
            display: table;
            width: 100%;
        }
        .images-column {
            display: table-cell;
            width: 33.33%;
            vertical-align: top;
            padding-right: 10px;
        }
        .image-item {
            margin-bottom: 10px;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            max-height: 150px;
            border: 1px solid #ddd;
        }
        .image-caption {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .page-break {
            page-break-before: always;
        }
        .footer {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TAR (Technical Activity Report)</h1>
        <h2>Daily Reports</h2>
    </div>

    <div class="period">
        Periode: {{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}
    </div>

    @foreach($dailyReports as $index => $report)
        @if($index > 0)
            <div class="page-break"></div>
        @endif
        
        <div class="report-item">
            <div class="report-header">
                <h3>{{ $report->unit->unit_code ?? 'N/A' }} - {{ $report->unit->unit_type ?? 'N/A' }}</h3>
            </div>

            <div class="info-grid">
                <div class="info-row">
                    <div class="info-label">Tanggal:</div>
                    <div class="info-value">{{ $report->date_in->format('d/m/Y') }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Jam Kerja:</div>
                    <div class="info-value">{{ $report->hour_in }} - {{ $report->hour_out }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Shift:</div>
                    <div class="info-value">{{ $report->shift }}</div>
                </div>
                @if($report->hm)
                <div class="info-row">
                    <div class="info-label">HM:</div>
                    <div class="info-value">{{ $report->hm }}</div>
                </div>
                @endif
                @if($report->problem)
                <div class="info-row">
                    <div class="info-label">Problem:</div>
                    <div class="info-value">{{ $report->problem }}</div>
                </div>
                @endif
                @if($report->problem_component)
                <div class="info-row">
                    <div class="info-label">Problem Component:</div>
                    <div class="info-value">{{ $report->problem_component }}</div>
                </div>
                @endif
                @if($report->problem_description)
                <div class="info-row">
                    <div class="info-label">Problem Description:</div>
                    <div class="info-value">{{ $report->problem_description }}</div>
                </div>
                @endif
            </div>

            @if($report->jobs->count() > 0)
            <div class="jobs-section">
                <div class="section-title">Pekerjaan yang Dilakukan:</div>
                <div class="jobs-list">
                    @foreach($report->jobs as $job)
                    <div class="job-item">
                        @if($job->highlight)
                            <span class="highlighted-job">{{ $job->job_description }}</span>
                        @else
                            • {{ $job->job_description }}
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            @if($report->technicians->count() > 0)
            <div class="technicians-section">
                <div class="section-title">Teknisi:</div>
                <div class="technicians-list">
                    @foreach($report->technicians as $technician)
                    <div>• {{ $technician->name }}</div>
                    @endforeach
                </div>
            </div>
            @endif

            @if($report->images->count() > 0)
            <div class="images-section">
                <div class="section-title">Dokumentasi:</div>
                <div class="images-grid">
                    <div class="images-column">
                        <strong>Gambar Sebelum:</strong>
                        @foreach($report->images->where('type', 'before') as $image)
                        <div class="image-item">
                            <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="Before Image">
                            <div class="image-caption">{{ $image->image_path }}</div>
                        </div>
                        @endforeach
                    </div>
                    <div class="images-column">
                        <strong>Gambar Sesudah:</strong>
                        @foreach($report->images->where('type', 'after') as $image)
                        <div class="image-item">
                            <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="After Image">
                            <div class="image-caption">{{ $image->image_path }}</div>
                        </div>
                        @endforeach
                    </div>
                    <div class="images-column">
                        <strong>Gambar Unit:</strong>
                        @foreach($report->images->where('type', 'unit') as $image)
                        <div class="image-item">
                            <img src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="Unit Image">
                            <div class="image-caption">{{ $image->image_path }}</div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>
    @endforeach

    <div class="footer">
        Generated on {{ now()->format('d/m/Y H:i:s') }}
    </div>
</body>
</html>
