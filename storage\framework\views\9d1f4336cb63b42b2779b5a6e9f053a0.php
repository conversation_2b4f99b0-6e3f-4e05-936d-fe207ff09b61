<?php $__env->startSection('title', 'Unit Recap'); ?>
<?php $__env->startSection('resourcesite'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/unit-recap.js']); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/css/unit-recap.css']); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('contentsite'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('sites.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('daily-reports.index')); ?>">Daily Reports</a></li>
                        <li class="breadcrumb-item active">Unit Recap</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    Unit Recap Dashboard
                    <a href="<?php echo e(route('daily-reports.index')); ?>" class="btn btn-sm btn-secondary ms-2">
                        <i class="mdi mdi-arrow-left"></i> Back to Daily Reports
                    </a>
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Chart Section -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h4 class="header-title">Problem Component Analysis</h4>
                        </div>
                        <div class="col-md-4">
                            <div class="row">
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="chart-start-date" placeholder="Start Date">
                                </div>
                                <div class="col-6">
                                    <input type="date" class="form-control form-control-sm" id="chart-end-date" placeholder="End Date">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Skeleton for Chart -->
                    <div id="chart-loading-skeleton" class="d-none">
                        <div class="skeleton-loader">
                            <div class="skeleton-chart"></div>
                        </div>
                    </div>

                    <!-- Chart Container -->
                    <div class="chart-container" style="position: relative; height: 400px;">
                        <canvas id="problemComponentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unit Selection Section -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h4 class="header-title mb-3">Unit Search</h4>
                    
                    <!-- Unit Search -->
                    <div class="mb-3">
                        <label for="unit-search" class="form-label">Search Unit</label>
                        <div class="position-relative">
                            <input type="text" class="form-control" id="unit-search" placeholder="Type unit code or type..." autocomplete="off">
                            <div id="unit-dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                    </div>

                    <!-- Selected Unit Info -->
                    <div id="selected-unit-info" class="d-none">
                        <div class="alert alert-info">
                            <strong>Selected Unit:</strong>
                            <div id="selected-unit-details"></div>
                        </div>
                    </div>

                    <!-- Report Dates List -->
                    <div id="report-dates-container" class="d-none">
                        <h6 class="mb-2">Available Report Dates:</h6>
                        <div id="report-dates-list" class="list-group" style="max-height: 300px; overflow-y: auto;">
                            <!-- Dates will be loaded here -->
                        </div>
                    </div>

                    <!-- Loading indicator -->
                    <div id="dates-loading" class="text-center py-3 d-none">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted small">Loading dates...</p>
                    </div>

                    <!-- Empty state -->
                    <div id="no-dates-message" class="text-center py-4 d-none">
                        <i class="mdi mdi-calendar-blank" style="font-size: 48px; color: #ccc;"></i>
                        <p class="text-muted mt-2">No reports found for this unit</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Problem Component Details Modal -->
<div class="modal fade" id="problem-component-modal" tabindex="-1" aria-labelledby="problem-component-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="problem-component-modal-label">Problem Component Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner -->
                <div id="modal-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading details...</p>
                </div>

                <!-- Content will be loaded here -->
                <div id="modal-content" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Unit</th>
                                    <th>HM</th>
                                    <th>Problem</th>
                                    <th>Problem Description</th>
                                    <th>Job Description</th>
                                    <th>Start</th>
                                    <th>Finish</th>
                                    <th>Shift</th>
                                    <th>Down Time</th>
                                    <th>Technicians</th>
                                </tr>
                            </thead>
                            <tbody id="modal-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Unit Date Reports Modal -->
<div class="modal fade" id="unit-date-modal" tabindex="-1" aria-labelledby="unit-date-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unit-date-modal-label">Unit Reports</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Loading spinner -->
                <div id="unit-modal-loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading reports...</p>
                </div>

                <!-- Content will be loaded here -->
                <div id="unit-modal-content" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Unit</th>
                                    <th>HM</th>
                                    <th>Problem</th>
                                    <th>Problem Component</th>
                                    <th>Problem Description</th>
                                    <th>Job Description</th>
                                    <th>Start</th>
                                    <th>Finish</th>
                                    <th>Shift</th>
                                    <th>Down Time</th>
                                    <th>Technicians</th>
                                </tr>
                            </thead>
                            <tbody id="unit-modal-table-body">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('sites.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/unit-recap/index.blade.php ENDPATH**/ ?>